defmodule Drops.Logger.Formatter do
  @moduledoc """
  Custom formatters for Drops operations debug logging.

  This module provides formatters that work with built-in Erlang logger handlers
  to format operation debug logs in different formats.

  ## Formatter Types

  * `:string` - Human-readable string format with message and metadata (default)
  * `:json` - JSON format with message and metadata as structured data

  ## Usage

  These formatters are used automatically by the Drops logger system when
  configuring built-in handlers for console and file logging.
  """

  @doc """
  Main formatter function called by built-in logger handlers.

  This function dispatches to the appropriate formatter based on the config.
  """
  def format(log_event, config) when is_atom(config) do
    # When config is an atom, it's the formatter type
    # Built-in handlers add their own newlines, so don't add them in formatter
    format_with_type(log_event, config, %{add_newline: false})
  end

  def format(log_event, config) when is_map(config) do
    # When config is a map, extract the formatter type and options
    formatter_type = Map.get(config, :formatter, :string)
    add_newline = Map.get(config, :add_newline, false)
    format_with_type(log_event, formatter_type, %{add_newline: add_newline})
  end

  defp format_with_type(log_event, :string, opts) do
    string(log_event, opts)
  end

  defp format_with_type(log_event, :json, opts) do
    json(log_event, opts)
  end

  defp format_with_type(log_event, _, opts) do
    # Default to string format
    string(log_event, opts)
  end

  @doc """
  String formatter for operation debug logs.

  Formats logs in a human-readable string format with operation metadata.
  Only processes logs that have operation-related metadata.

  ## Format

  The output format is: `[level] message metadata`

  Where metadata includes operation and step information when available.
  """
  def string(log_event, _config) do
    if is_operation_log?(log_event) do
      level = Map.get(log_event, :level, :debug)
      message = extract_message(log_event)

      # The built-in logger_std_h doesn't pass our formatter config correctly,
      # so we get it directly from the application environment
      app_config = Application.get_env(:drops, :logger, [])
      formatter_config = Keyword.get(app_config, :formatter, %{})

      # Use configured metadata fields, defaulting to [:operation, :step]
      metadata_fields = Keyword.get(app_config, :metadata, [:operation, :step])
      metadata = extract_metadata(log_event, metadata_fields)

      colorize = should_colorize?(formatter_config)

      formatted =
        if colorize do
          format_with_colors(level, message, metadata)
        else
          "[#{level}] #{message}#{metadata}"
        end

      # Always add newline for console output
      formatted <> "\n"
    else
      # Return empty string for non-operation logs to filter them out
      ""
    end
  end

  @doc """
  JSON formatter for operation debug logs.

  Formats logs as JSON with structured metadata.
  Only processes logs that have operation-related metadata.

  ## Format

  The output is a JSON object with the following structure:

      {
        "level": "debug",
        "message": "Operation message",
        "metadata": {
          "operation": "OperationName",
          "step": "step_name"
        },
        "timestamp": 1234567890123
      }

  If Jason is not available, falls back to using `inspect/1`.
  """
  def json(log_event, config) do
    if is_operation_log?(log_event) do
      level = Map.get(log_event, :level, :debug)
      message = extract_message(log_event)

      # Use configured metadata fields, defaulting to [:operation, :step]
      app_config = Application.get_env(:drops, :logger, [])
      metadata_fields = Keyword.get(app_config, :metadata, [:operation, :step])
      metadata = extract_metadata_map(log_event, metadata_fields)

      json_data = %{
        level: level,
        message: message,
        metadata: metadata,
        timestamp: System.system_time(:millisecond)
      }

      result =
        case Code.ensure_loaded(Jason) do
          {:module, Jason} ->
            try do
              Jason.encode!(json_data)
            rescue
              _ ->
                # Fallback to inspect if JSON encoding fails
                inspect(json_data)
            end

          {:error, _} ->
            # Fallback to inspect if Jason is not available
            inspect(json_data)
        end

      # Add newline if configured to do so (default for built-in handlers)
      # Handle nil config for backward compatibility
      add_newline = if config, do: Map.get(config, :add_newline, true), else: true

      if add_newline do
        result <> "\n"
      else
        result
      end
    else
      # Return empty string for non-operation logs to filter them out
      ""
    end
  end

  # Private functions

  defp is_operation_log?(log_event) do
    meta = Map.get(log_event, :meta, %{})
    # Check if the log has operation-related metadata
    Map.has_key?(meta, :operation) or Map.has_key?(meta, :step)
  end

  defp should_colorize?(config) do
    # Check if colorization is explicitly enabled in config
    colorize_config = Map.get(config, :colorize, false)

    # Only colorize if explicitly enabled AND we're outputting to a terminal
    colorize_config && IO.ANSI.enabled?()
  end

  defp format_with_colors(level, message, metadata) do
    level_color = get_level_color(level)
    reset = IO.ANSI.reset()

    # Format: [level] message metadata
    # Colors: level in color, message in normal, metadata in faint
    "#{level_color}[#{level}]#{reset} #{message}#{IO.ANSI.green()}#{metadata}#{reset}"
  end

  defp get_level_color(:debug), do: IO.ANSI.cyan()
  defp get_level_color(:info), do: IO.ANSI.normal()
  defp get_level_color(:warning), do: IO.ANSI.yellow()
  defp get_level_color(:warn), do: IO.ANSI.yellow()
  defp get_level_color(:error), do: IO.ANSI.red()
  defp get_level_color(:critical), do: IO.ANSI.red()
  defp get_level_color(:alert), do: IO.ANSI.red()
  defp get_level_color(:emergency), do: IO.ANSI.red()
  defp get_level_color(_), do: IO.ANSI.normal()

  defp extract_message(%{msg: msg}) when is_binary(msg), do: msg
  defp extract_message(%{msg: msg}) when is_list(msg), do: IO.iodata_to_binary(msg)

  defp extract_message(%{msg: {format, args}}) when is_list(args),
    do: :io_lib.format(format, args) |> IO.iodata_to_binary()

  # Handle {:string, message} tuples from Logger calls
  defp extract_message(%{msg: {:string, msg}}) when is_binary(msg), do: msg

  defp extract_message(%{msg: msg}), do: inspect(msg)
  defp extract_message(_), do: ""

  defp extract_metadata(log_event, metadata_keys) do
    meta = Map.get(log_event, :meta, %{})

    # Separate context from other metadata
    {context_items, other_items} =
      metadata_keys
      |> Enum.map(fn key ->
        case Map.get(meta, key) do
          nil -> nil
          value -> {key, value}
        end
      end)
      |> Enum.filter(& &1)
      |> Enum.split_with(fn {key, _value} -> key == :context end)

    # Format other metadata normally
    other_metadata =
      other_items
      |> Enum.map(fn {key, value} -> "#{key}=#{inspect(value)}" end)
      |> case do
        [] -> ""
        items -> " " <> Enum.join(items, " ")
      end

    # Format context specially on a new line with pretty printing
    context_metadata =
      case context_items do
        [] ->
          ""

        [{:context, context}] ->
          formatted_context = inspect(context, pretty: true, width: 80, limit: :infinity)
          "\n  context: #{formatted_context}"
      end

    other_metadata <> context_metadata
  end

  defp extract_metadata_map(log_event, metadata_keys) do
    meta = Map.get(log_event, :meta, %{})

    metadata_keys
    |> Enum.reduce(%{}, fn key, acc ->
      case Map.get(meta, key) do
        nil -> acc
        value -> Map.put(acc, key, value)
      end
    end)
  end
end
