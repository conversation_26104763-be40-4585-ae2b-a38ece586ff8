defprotocol Drops.Operations.Extensions.Debug.MetadataDumper do
  @moduledoc """
  Protocol for dumping metadata values into logs.

  This protocol allows custom types to provide a brief and readable representation
  for debug logging purposes. It's particularly useful for complex data structures
  that would otherwise produce verbose or unreadable output in logs.

  ## Implementation

  Implement this protocol for your custom types to control how they appear in debug logs:

      defimpl Drops.Operations.Extensions.Debug.MetadataDumper, for: MyStruct do
        def dump(%MyStruct{id: id, name: name}) do
          "MyStruct(id=\#{id}, name=\#{inspect(name)})"
        end
      end

  ## Built-in Implementations

  The protocol includes implementations for common types:

  - `Ecto.Changeset` - Shows validity, changes, and errors
  - Basic types (String, Integer, Atom, etc.) - Use inspect/1
  """

  @doc """
  Dumps a value into a brief, readable string representation for logging.

  ## Parameters

  - `value` - The value to dump

  ## Returns

  A string representation suitable for debug logging.
  """
  @spec dump(any()) :: String.t()
  def dump(value)
end

defimpl Drops.Operations.Extensions.Debug.MetadataDumper,
  for: [BitString, Integer, Float, Atom] do
  def dump(value), do: inspect(value)
end

defimpl Drops.Operations.Extensions.Debug.MetadataDumper, for: List do
  def dump(list) when length(list) <= 3, do: inspect(list)
  def dump(list), do: "[#{length(list)} items]"
end

defimpl Drops.Operations.Extensions.Debug.MetadataDumper, for: Map do
  def dump(map) when map_size(map) <= 3, do: inspect(map)
  def dump(map), do: "%{#{map_size(map)} keys}"
end

if Code.ensure_loaded?(Ecto.Changeset) do
  defimpl Drops.Operations.Extensions.Debug.MetadataDumper, for: Ecto.Changeset do
    def dump(%Ecto.Changeset{valid?: valid?, changes: changes, errors: errors}) do
      status = if valid?, do: "valid", else: "invalid"
      change_count = map_size(changes)
      error_count = length(errors)

      parts = ["Changeset(#{status})"]

      parts =
        if change_count > 0 do
          parts ++ ["#{change_count} changes"]
        else
          parts
        end

      parts =
        if error_count > 0 do
          parts ++ ["#{error_count} errors"]
        else
          parts
        end

      Enum.join(parts, ", ")
    end
  end
end

defmodule Drops.Operations.Extensions.Debug do
  @moduledoc """
  Debug extension for Operations framework.

  This extension provides debug logging for Operations by leveraging telemetry events
  and a custom log handler. When enabled, it automatically attaches telemetry handlers
  that log operation and step execution details using a configurable debug handler.

  ## Features

  - Automatic operation-level debug logging (start/stop events)
  - Automatic step-level debug logging (start/stop events for all steps)
  - Configurable log handler (console, file, or memory)
  - Metadata includes operation module, step name, and execution context
  - Built on top of the Telemetry extension
  - Smart metadata dumping for complex types via MetadataDumper protocol

  ## Configuration

  The debug handler can be configured via application environment:

      config :drops, :logger,
        handler: :file,
        file: "log/operations.log",
        level: :debug,
        format: "[$level] $message $metadata\\n",
        metadata: [:operation, :step]

  ## Usage

  ### Enable Debug Logging

  Enable debug logging with default behavior:

      defmodule CreateUser do
        use Drops.Operations.Command, debug: true

        steps do
          @impl true
          def execute(%{params: params}) do
            {:ok, create_user(params)}
          end
        end
      end

  ### Custom Configuration

  You can also pass configuration options:

      defmodule CreateUser do
        use Drops.Operations.Command, debug: [identifier: :my_app]

        steps do
          @impl true
          def execute(%{params: params}) do
            {:ok, create_user(params)}
          end
        end
      end

  ## Debug Output

  The extension logs the following events at debug level:

  ### Operation-Level Events

  - Operation start: "OperationName started"
  - Operation stop: "OperationName succeeded in Xms"
  - Operation exception: "OperationName failed in Xms"

  ### Step-Level Events

  - Step start: "OperationName.step_name started"
  - Step stop: "OperationName.step_name succeeded in Xms"
  - Step exception: "OperationName.step_name failed in Xms"

  ## Implementation Details

  This extension works by:

  1. Enabling telemetry with both operation and step-level instrumentation
  2. Attaching telemetry handlers that log events using the custom debug handler
  3. Automatically cleaning up handlers when the operation module is unloaded

  The extension uses the Telemetry extension internally and attaches handlers
  during module compilation.
  """
  use Drops.Operations.Extension

  require Logger

  alias Drops.Operations.Extensions.Debug.MetadataDumper

  @depends_on [Drops.Operations.Extensions.Telemetry]

  @impl true
  @spec enable?(keyword()) :: boolean()
  def enable?(opts) do
    case Keyword.get(opts, :debug, false) do
      false -> false
      true -> true
      config when is_list(config) -> true
    end
  end

  @impl true
  @spec default_opts(keyword()) :: keyword()
  def default_opts(opts) do
    debug_config = Keyword.get(opts, :debug, false)

    case debug_config do
      false ->
        []

      true ->
        # Enable telemetry with all steps instrumented
        [telemetry: [steps: :all]]

      config when is_list(config) ->
        # Pass through custom identifier but ensure all steps are instrumented
        identifier = Keyword.get(config, :identifier, :drops)
        [telemetry: [identifier: identifier, steps: :all]]
    end
  end

  @impl true
  @spec unit_of_work(Drops.Operations.UnitOfWork.t(), keyword()) ::
          Drops.Operations.UnitOfWork.t()
  def unit_of_work(uow, opts) do
    debug_config = Keyword.get(opts, :debug, false)

    case debug_config do
      false ->
        uow

      _ ->
        # Attach debug handlers immediately during compilation
        # This ensures they're available when the operation runs
        attach_debug_handlers(uow.module, debug_config)
        uow
    end
  end

  @impl true
  @spec using() :: Macro.t()
  def using do
    quote do
      # Ensure handlers are cleaned up when module is unloaded
      @before_compile Drops.Operations.Extensions.Debug
    end
  end

  @impl true
  @spec helpers() :: Macro.t()
  def helpers do
    quote do
      # No additional helpers needed
    end
  end

  @impl true
  @spec steps() :: Macro.t()
  def steps do
    quote do
      # No additional steps needed
    end
  end

  defmacro __before_compile__(_env) do
    quote do
      def __debug_handler_id__, do: "debug-#{__MODULE__}"

      # Clean up handlers when module is unloaded
      @on_load :__attach_debug_handlers__
      @before_compile :__detach_debug_handlers__

      def __attach_debug_handlers__ do
        # Handlers are attached in unit_of_work/2, this is just a placeholder
        :ok
      end

      def __detach_debug_handlers__ do
        try do
          :telemetry.detach(__debug_handler_id__())
        rescue
          _ -> :ok
        end
      end
    end
  end

  # Private functions

  defp attach_debug_handlers(operation_module, debug_config) do
    identifier = get_identifier(debug_config)
    handler_id = "debug-#{operation_module}"

    # Define the events we want to listen to
    events = [
      [identifier, :operation, :start],
      [identifier, :operation, :stop],
      [identifier, :operation, :exception],
      [identifier, :operation, :step, :start],
      [identifier, :operation, :step, :stop],
      [identifier, :operation, :step, :exception]
    ]

    # Attach the handler
    :telemetry.attach_many(
      handler_id,
      events,
      &__MODULE__.handle_debug_event/4,
      %{operation_module: operation_module}
    )
  end

  defp get_identifier(debug_config) do
    case debug_config do
      true -> :drops
      config when is_list(config) -> Keyword.get(config, :identifier, :drops)
      _ -> :drops
    end
  end

  @doc false
  def handle_debug_event(
        [_identifier, :operation, :start],
        measurements,
        metadata,
        _config
      ) do
    operation_name = format_operation_name(metadata.operation)
    context = format_context(metadata.context)

    Logger.debug("#{operation_name} started",
      operation: operation_name,
      step: metadata.step,
      context: context,
      system_time: measurements.system_time
    )
  end

  def handle_debug_event(
        [_identifier, :operation, :stop],
        measurements,
        metadata,
        _config
      ) do
    operation_name = format_operation_name(metadata.operation)
    duration_us = System.convert_time_unit(measurements.duration, :native, :microsecond)
    duration_display = format_duration(duration_us)
    context = format_context(metadata.context)

    Logger.debug(
      "#{operation_name} succeeded in #{duration_display}",
      operation: operation_name,
      step: metadata.step,
      context: context,
      duration_us: duration_us
    )
  end

  def handle_debug_event(
        [_identifier, :operation, :exception],
        measurements,
        metadata,
        _config
      ) do
    operation_name = format_operation_name(metadata.operation)
    duration_us = System.convert_time_unit(measurements.duration, :native, :microsecond)
    duration_display = format_duration(duration_us)
    context = format_context(metadata.context)

    Logger.debug(
      "#{operation_name} failed in #{duration_display}",
      operation: operation_name,
      step: metadata.step,
      context: context,
      duration_us: duration_us,
      kind: metadata.kind,
      reason: metadata.reason
    )
  end

  def handle_debug_event(
        [_identifier, :operation, :step, :start],
        measurements,
        metadata,
        _config
      ) do
    operation_name = format_operation_name(metadata.operation)
    context = format_context(metadata.context)

    Logger.debug("#{operation_name}.#{metadata.step} started",
      operation: operation_name,
      step: metadata.step,
      context: context,
      system_time: measurements.system_time
    )
  end

  def handle_debug_event(
        [_identifier, :operation, :step, :stop],
        measurements,
        metadata,
        _config
      ) do
    operation_name = format_operation_name(metadata.operation)
    duration_us = System.convert_time_unit(measurements.duration, :native, :microsecond)
    duration_display = format_duration(duration_us)
    context = format_context(metadata.context)

    Logger.debug(
      "#{operation_name}.#{metadata.step} succeeded in #{duration_display}",
      operation: operation_name,
      step: metadata.step,
      context: context,
      duration_us: duration_us
    )
  end

  def handle_debug_event(
        [_identifier, :operation, :step, :exception],
        measurements,
        metadata,
        _config
      ) do
    operation_name = format_operation_name(metadata.operation)
    duration_us = System.convert_time_unit(measurements.duration, :native, :microsecond)
    duration_display = format_duration(duration_us)
    context = format_context(metadata.context)

    Logger.debug(
      "#{operation_name}.#{metadata.step} failed in #{duration_display}",
      operation: operation_name,
      step: metadata.step,
      context: context,
      duration_us: duration_us,
      kind: metadata.kind,
      reason: metadata.reason
    )
  end

  # Private helper functions

  defp format_operation_name(operation) when is_atom(operation) do
    operation
    |> to_string()
    |> String.replace_prefix("Elixir.", "")
  end

  defp format_operation_name(operation), do: to_string(operation)

  defp format_context(context) do
    try do
      MetadataDumper.dump(context)
    rescue
      Protocol.UndefinedError ->
        # Fallback to inspect for types without MetadataDumper implementation
        inspect(context, limit: 50, printable_limit: 100)
    end
  end

  defp format_duration(duration_us) when duration_us < 1000, do: "#{duration_us}μs"

  defp format_duration(duration_us) when duration_us < 1_000_000,
    do: "#{Float.round(duration_us / 1000, 2)}ms"

  defp format_duration(duration_us), do: "#{Float.round(duration_us / 1_000_000, 2)}s"
end
