#!/usr/bin/env elixir

# Example demonstrating the Debug extension with MetadataDumper protocol
# Run with: mix run examples/debug_with_metadata.exs

# Configure debug logging
Application.put_env(:drops, :logger,
  handler: :console,
  level: :debug,
  formatter: %{format: :string, colorize: true},
  metadata: [:operation, :step, :context]
)

# Start the logger
Drops.Logger.add_handler()

# Define a test operation that uses debug logging
defmodule ExampleOperation do
  use Drops.Operations.Command, debug: true

  schema do
    %{
      required(:name) => string(),
      required(:age) => integer(),
      optional(:changeset) => any()
    }
  end

  steps do
    @impl true
    def execute(%{params: params} = context) do
      IO.puts("\n=== Debug Extension Example ===")
      IO.puts("This operation demonstrates:")
      IO.puts("1. Clean operation module names (no 'Elixir.' prefix)")
      IO.puts("2. Smart metadata dumping with MetadataDumper protocol")
      IO.puts("3. Colorized debug output (if terminal supports it)")
      IO.puts("\nWatch the debug logs below:\n")

      # Simulate some work with different types of metadata
      context_with_changeset =
        if Code.ensure_loaded?(Ecto.Changeset) do
          changeset = %Ecto.Changeset{
            valid?: false,
            changes: %{name: params.name, age: params.age},
            errors: [email: {"is required", []}]
          }

          Map.put(context, :changeset, changeset)
        else
          context
        end

      {:ok, %{
        message: "Operation completed successfully!",
        user: %{name: params.name, age: params.age},
        processed_at: DateTime.utc_now()
      }}
    end
  end
end

# Run the example
IO.puts("Starting debug extension example...")

result = ExampleOperation.call(%{
  params: %{
    name: "Alice",
    age: 30
  }
})

case result do
  {:ok, data} ->
    IO.puts("\n=== Operation Result ===")
    IO.inspect(data, pretty: true)

  {:error, reason} ->
    IO.puts("\n=== Operation Failed ===")
    IO.inspect(reason, pretty: true)
end

IO.puts("\n=== Example Complete ===")
IO.puts("Notice how the debug logs show:")
IO.puts("- Clean operation names without 'Elixir.' prefix")
IO.puts("- Smart metadata formatting for complex types")
IO.puts("- Proper duration formatting (μs, ms, s)")

if Code.ensure_loaded?(Ecto.Changeset) do
  IO.puts("- Ecto.Changeset formatted as 'Changeset(invalid), 2 changes, 1 errors'")
else
  IO.puts("- Install Ecto to see Changeset formatting in action")
end
