defmodule Drops.Operations.Extensions.DebugTest do
  use Drops.OperationCase, async: false

  alias Drops.Logger

  defmodule TestHandler do
    def handle_event(event, measurements, metadata, _config) do
      send(self(), {:telemetry_event, event, measurements, metadata})
    end
  end

  setup do
    # Configure debug handler to use memory for testing
    original_config = Application.get_env(:drops, :logger, [])

    Application.put_env(:drops, :logger,
      handler: :memory,
      level: :debug,
      format: "[$level] $message $metadata\n",
      metadata: [:operation, :step]
    )

    # Set logger configuration to allow debug logs
    Application.put_env(:logger, :level, :debug)
    :logger.set_primary_config(level: :debug)

    # Also set process-level logger to debug to override any process-level filtering
    Elixir.Logger.configure(level: :debug)

    # Suppress debug output from default console handler during tests
    :logger.set_handler_config(:default, :level, :warning)

    # Add the debug handler to the logger system
    Logger.add_handler()

    # Clear any existing logs
    Logger.clear_logs()

    on_exit(fn ->
      Logger.remove_handler()
      :logger.set_handler_config(:default, :level, :all)
      Application.put_env(:drops, :logger, original_config)
      Logger.clear_logs()
    end)

    :ok
  end

  describe "when debug is enabled" do
    operation type: :command, debug: true do
      schema do
        %{
          required(:name) => string(),
          required(:age) => integer()
        }
      end

      steps do
        @impl true
        def execute(context) do
          {:ok, context}
        end
      end
    end

    test "logging operation start/stop events by default", %{operation: operation} do
      context = %{params: %{name: "test", age: 30}}

      # Clear logs before test
      Logger.clear_logs()

      {:ok, _result} = operation.call(context)

      # Get captured logs
      logs = Logger.get_logs()
      log_output = Enum.join(logs, "")

      # Should log operation start/stop events (first and last steps)
      assert log_output =~ "Starting step conform in #{operation}"
      assert log_output =~ "Completed step conform in"
      assert log_output =~ "Starting step execute in #{operation}"
      assert log_output =~ "Completed step execute in"
    end

    test "logging operation's step start/stop events by default", %{operation: operation} do
      context = %{params: %{name: "test", age: 30}}

      # Clear logs before test
      Logger.clear_logs()

      {:ok, _result} = operation.call(context)

      # Get captured logs
      logs = Logger.get_logs()
      log_output = Enum.join(logs, "")

      # Should log all step start/stop events
      assert log_output =~ "Starting step conform in #{operation}"
      assert log_output =~ "Completed step conform in"
      assert log_output =~ "Starting step execute in #{operation}"
      assert log_output =~ "Completed step execute in"
    end

    test "logs duration with proper precision formatting", %{operation: operation} do
      context = %{params: %{name: "test", age: 30}}

      # Clear logs before test
      Logger.clear_logs()

      {:ok, _result} = operation.call(context)

      # Get captured logs
      logs = Logger.get_logs()
      log_output = Enum.join(logs, "")

      # Should contain duration information with proper units
      # Fast operations should show microseconds (μs)
      # Slower operations might show milliseconds (ms) or seconds (s)
      duration_patterns = [
        # microseconds
        ~r/Completed step \w+ in \d+μs/,
        # milliseconds
        ~r/Completed step \w+ in \d+\.\d+ms/,
        # seconds
        ~r/Completed step \w+ in \d+\.\d+s/
      ]

      # At least one duration pattern should match
      assert Enum.any?(duration_patterns, fn pattern ->
               Regex.match?(pattern, log_output)
             end),
             "Expected to find duration with proper unit formatting in: #{log_output}"
    end

    test "logs contain proper metadata for operation filtering", %{operation: operation} do
      context = %{params: %{name: "test", age: 30}}

      # Clear logs before test
      Logger.clear_logs()

      {:ok, _result} = operation.call(context)

      # Get captured logs
      logs = Logger.get_logs()

      # All logs should contain operation metadata (required for formatter filtering)
      assert length(logs) > 0, "Expected debug logs to be captured"

      Enum.each(logs, fn log ->
        # Each log should contain operation metadata (without Elixir. prefix)
        operation_name = operation |> to_string() |> String.replace_prefix("Elixir.", "")

        assert log =~ "operation=#{operation_name}",
               "Expected log to contain operation metadata: #{log}"

        # Step logs should contain step metadata
        if log =~ "Starting step" or log =~ "Completed step" do
          assert log =~ "step=",
                 "Expected step log to contain step metadata: #{log}"
        end
      end)
    end
  end

  describe "when debug is disabled" do
    operation type: :command, debug: false do
      schema do
        %{
          required(:name) => string(),
          required(:age) => integer()
        }
      end

      steps do
        @impl true
        def execute(context) do
          {:ok, context}
        end
      end
    end

    test "does not log debug messages", %{operation: operation} do
      context = %{params: %{name: "test", age: 30}}

      # Clear logs before test
      Logger.clear_logs()

      {:ok, _result} = operation.call(context)

      # Get captured logs
      logs = Logger.get_logs()

      # Should not contain debug logs from the Debug extension
      assert logs == []
    end
  end

  describe "when debug is enabled with custom identifier" do
    operation type: :command, debug: [identifier: :my_app] do
      schema do
        %{
          required(:name) => string(),
          required(:age) => integer()
        }
      end

      steps do
        @impl true
        def execute(context) do
          {:ok, context}
        end
      end
    end

    test "logs debug messages with custom identifier", %{operation: operation} do
      context = %{params: %{name: "test", age: 30}}

      # Clear logs before test
      Logger.clear_logs()

      {:ok, _result} = operation.call(context)

      # Get captured logs
      logs = Logger.get_logs()
      log_output = Enum.join(logs, "")

      # Should log all step start/stop events
      assert log_output =~ "Starting step conform in #{operation}"
      assert log_output =~ "Completed step conform in"
      assert log_output =~ "Starting step execute in #{operation}"
      assert log_output =~ "Completed step execute in"
    end
  end

  describe "formatter colorization" do
    test "supports colorization option" do
      log_event = %{
        level: :debug,
        msg: {:string, "Test message"},
        meta: %{operation: "TestOp", step: "test"}
      }

      # Test with colorization enabled
      original_config = Application.get_env(:drops, :logger, [])

      try do
        # Set colorization enabled
        Application.put_env(
          :drops,
          :logger,
          Keyword.put(original_config, :formatter, %{colorize: true, format: :string})
        )

        colored_output = Drops.Logger.Formatter.string(log_event, %{})

        # Check if ANSI is enabled in test environment
        if IO.ANSI.enabled?() do
          # Cyan color for debug level
          assert String.contains?(colored_output, "\e[36m[debug]\e[0m")
          # Faint color for metadata
          assert String.contains?(colored_output, "\e[2m")
        else
          # If ANSI is disabled, should fall back to plain format
          refute String.contains?(colored_output, "\e[")
        end

        # Test with colorization disabled
        Application.put_env(
          :drops,
          :logger,
          Keyword.put(original_config, :formatter, %{colorize: false, format: :string})
        )

        plain_output = Drops.Logger.Formatter.string(log_event, %{})
        # No ANSI escape codes
        refute String.contains?(plain_output, "\e[")
        assert String.contains?(plain_output, "[debug]")
      after
        # Restore original config
        Application.put_env(:drops, :logger, original_config)
      end
    end

    test "uses different colors for different log levels" do
      # Skip this test if ANSI is not enabled
      if IO.ANSI.enabled?() do
        original_config = Application.get_env(:drops, :logger, [])

        try do
          # Set colorization enabled
          Application.put_env(
            :drops,
            :logger,
            Keyword.put(original_config, :formatter, %{colorize: true, format: :string})
          )

          test_cases = [
            # cyan
            {:debug, "\e[36m"},
            # normal
            {:info, "\e[0m"},
            # yellow
            {:warning, "\e[33m"},
            # red
            {:error, "\e[31m"}
          ]

          for {level, expected_color} <- test_cases do
            log_event = %{
              level: level,
              msg: {:string, "Test message"},
              meta: %{operation: "TestOp", step: "test"}
            }

            output = Drops.Logger.Formatter.string(log_event, %{})

            assert String.contains?(output, expected_color),
                   "Expected #{level} to contain color code #{expected_color}"
          end
        after
          # Restore original config
          Application.put_env(:drops, :logger, original_config)
        end
      end
    end
  end
end
